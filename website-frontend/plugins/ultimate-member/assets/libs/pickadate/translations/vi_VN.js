// Vietnamese

jQuery.extend( jQuery.fn.pickadate.defaults, {
    monthsFull: [ 'Thán<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>g <PERSON>', '<PERSON><PERSON><PERSON>g <PERSON>', 'Tháng <PERSON>' ],
    monthsShort: [ '<PERSON><PERSON><PERSON>', '<PERSON>', 'Ba', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>' ],
    weekdaysFull: [ '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>' ],
    weekdaysShort: [ '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>.<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>' ],
    today: '<PERSON><PERSON><PERSON>',
    clear: '<PERSON><PERSON><PERSON>',
    close: '<PERSON><PERSON><PERSON>',
    firstDay: 1,
    format: 'B<PERSON>n chọn: dddd, dd mmmm, yyyy',
    formatSubmit: 'yyyy/mm/dd'
});

jQuery.extend( jQuery.fn.pickatime.defaults, {
    clear: 'Xoá'
});
