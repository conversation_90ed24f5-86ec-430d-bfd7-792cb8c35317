jQuery(document).ready(function(){jQuery(".um-profile.um-viewing .um-profile-body .um-row").each(function(){var e=jQuery(this);0==e.find(".um-field").length&&(e.prev(".um-row-heading").remove(),e.remove())}),jQuery(".um-profile.um-viewing .um-profile-body").length&&0==jQuery(".um-profile.um-viewing .um-profile-body").find(".um-field").length&&(jQuery(".um-profile.um-viewing .um-profile-body").find(".um-row-heading,.um-row").remove(),jQuery(".um-profile-note").show()),jQuery(document.body).on("click",".um-profile-save",function(e){return e.preventDefault(),jQuery(this).parents(".um.um-profile.um-editing").find("form").trigger("submit"),!1}),jQuery(document.body).on("click",".um-profile-edit-a",function(e){jQuery(this).addClass("active")}),jQuery(document.body).on("click",".um-cover a.um-cover-add, .um-photo a",function(e){e.preventDefault()}),jQuery(document.body).on("click",".um-photo-modal",function(e){e.preventDefault();e=jQuery(this).attr("data-src");return um_new_modal("um_view_photo","fit",!0,e),!1}),jQuery(document.body).on("click",".um-reset-profile-photo",function(e){let t=jQuery(this);var r=t.data("user_id"),u=t.parents("ul").find('.um-manual-trigger[data-parent=".um-profile-photo"]'),i=u.data("alt_text");return u.data("alt_text",u.text()).text(i),jQuery(".um-profile-photo-img img").attr("src",t.data("default_src")),jQuery.ajax({url:wp.ajax.settings.url,type:"post",data:{action:"um_delete_profile_photo",user_id:r,nonce:um_scripts.nonce},success:function(){t.removeClass("um-is-visible").hide()}}),UM.dropdown.hideAll(),!1}),jQuery(document.body).on("click",".um-reset-cover-photo",function(e){let t=jQuery(this);var r=t.data("user_id"),u=t.parents("ul").find('.um-manual-trigger[data-parent=".um-cover"]'),i=u.data("alt_text");return u.data("alt_text",u.text()).text(i),jQuery(".um-cover-overlay").hide(),jQuery(".um-cover-e").html('<a href="javascript:void(0);" class="um-cover-add" style="height: 370px;"><span class="um-cover-add-i"><i class="um-icon-plus um-tip-n" title="'+wp.i18n.__("Upload a cover photo","ultimate-member")+'"></i></span></a>'),um_responsive(),jQuery.ajax({url:wp.ajax.settings.url,type:"post",data:{action:"um_delete_cover_photo",user_id:r,nonce:um_scripts.nonce},success:function(){t.removeClass("um-is-visible").hide()}}),UM.dropdown.hideAll(),!1}),jQuery(document.body).on("change keyup","#um-meta-bio",function(){if(void 0!==jQuery(this).val()){var t=jQuery(this).data("character-limit"),r=jQuery(this).data("html");let e=t-jQuery(this).val().length;e=(e=1===parseInt(r)?t-jQuery(this).val().replace(/(<([^>]+)>)/gi,"").length:e)<0?0:e,jQuery("span.um-meta-bio-character span.um-bio-limit").text(e);r=e<5?"red":"";jQuery("span.um-meta-bio-character").css("color",r)}}),jQuery("#um-meta-bio").trigger("change"),jQuery(".um-profile form").each(function(){let t=jQuery(this).data("description_key");jQuery(this).find('textarea[name="'+t+'"]').length&&jQuery(document.body).on("change input",'textarea[name="'+t+'"]',function(e){jQuery(this).parents("form").find('textarea[name="'+t+'"]').each(function(){jQuery(this).val(e.currentTarget.value),jQuery("#um-meta-bio")[0]!==e.currentTarget&&jQuery("#um-meta-bio")[0]===jQuery(this)[0]&&jQuery(this).trigger("change")})})}),jQuery(".um-profile-edit a.um_delete-item").on("click",function(e){if(e.preventDefault(),!confirm(wp.i18n.__("Are you sure that you want to delete this user?","ultimate-member")))return!1}),jQuery(".um-profile-nav a").on("touchend",function(e){jQuery(e.currentTarget).trigger("click")})});