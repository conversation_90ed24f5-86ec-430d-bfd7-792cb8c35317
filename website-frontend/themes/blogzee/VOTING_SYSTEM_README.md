# WordPress主题点赞功能实现说明

## 功能概述

为Blogzee WordPress主题添加了完整的文章点赞/踩功能，支持以下特性：

1. ✅ 在文章详情页显示点赞和踩按钮
2. ✅ 只有已注册用户可以投票，未注册用户点击时显示提示
3. ✅ 每个用户对同一文章只能投票一次，支持更改投票或取消投票
4. ✅ 完整的国际化支持，默认语言为英语
5. ✅ 响应式设计，支持移动端
6. ✅ 实时AJAX更新，无需刷新页面

## 实现的文件修改

### 1. 数据库相关 (functions.php)
- 创建了 `wp_blogzee_post_likes` 数据库表
- 添加了 `blogzee_create_likes_table()` 函数
- 添加了 `blogzee_handle_post_vote()` AJAX处理函数
- 添加了 `blogzee_get_post_vote_counts()` 和 `blogzee_get_user_vote()` 辅助函数

### 2. 前端模板 (template-parts/content-single.php)
- 在文章内容后添加了点赞按钮HTML结构
- 集成了用户登录状态检查
- 显示当前投票数量和用户投票状态

### 3. JavaScript交互 (assets/js/theme.js)
- 添加了完整的AJAX投票处理逻辑
- 实现了按钮状态动态更新
- 添加了加载状态和错误处理
- 支持未登录用户提示

### 4. CSS样式 (style.css)
- 添加了美观的按钮样式
- 支持悬停、激活、加载等状态
- 完全响应式设计
- 包含反馈消息样式

### 5. 国际化支持 (languages/blogzee.pot)
- 添加了所有相关的翻译字符串
- 支持多语言环境

## 数据库表结构

```sql
CREATE TABLE wp_blogzee_post_likes (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    post_id bigint(20) NOT NULL,
    vote_type varchar(10) NOT NULL,
    vote_time datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY user_post (user_id, post_id),
    KEY post_id (post_id),
    KEY user_id (user_id)
);
```

## 使用方法

### 激活功能
1. 切换到Blogzee主题时，数据库表会自动创建
2. 功能会自动在所有单篇文章页面显示

### 用户体验
- **已登录用户**: 可以点击Like或Dislike按钮进行投票
- **未登录用户**: 点击按钮时会显示"需要登录才能投票"的提示
- **重复投票**: 
  - 点击相同按钮会取消投票
  - 点击不同按钮会更改投票类型

### 管理员功能
- 可以通过数据库直接查看投票数据
- 支持通过WordPress后台管理用户投票

## 测试功能

可以通过访问以下URL来测试功能（需要管理员权限）：
```
your-site.com/wp-content/themes/blogzee/test-voting-system.php?run_tests=1
```

## 自定义样式

如果需要修改按钮样式，可以在子主题的style.css中覆盖以下CSS类：

```css
.blogzee-post-voting { /* 投票容器 */ }
.vote-btn { /* 投票按钮基础样式 */ }
.like-btn { /* 点赞按钮 */ }
.dislike-btn { /* 踩按钮 */ }
.vote-btn.active { /* 激活状态 */ }
```

## 安全性

- 使用WordPress nonce验证防止CSRF攻击
- 严格的用户权限检查
- 数据库查询使用预处理语句防止SQL注入
- 输入数据经过严格验证和清理

## 性能优化

- 使用数据库索引优化查询性能
- AJAX请求减少页面刷新
- 合理的缓存策略
- 最小化数据库查询次数

## 兼容性

- 兼容WordPress 5.0+
- 支持所有现代浏览器
- 完全响应式设计
- 支持RTL语言

## 故障排除

### 常见问题

1. **按钮不显示**
   - 检查是否在单篇文章页面
   - 确认主题文件修改正确

2. **点击无反应**
   - 检查JavaScript控制台错误
   - 确认AJAX URL配置正确

3. **数据库错误**
   - 检查数据库表是否创建成功
   - 确认数据库用户权限

4. **样式问题**
   - 检查CSS文件是否正确加载
   - 确认没有样式冲突

### 调试模式

在wp-config.php中启用调试模式：
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 更新日志

### v1.0.0 (2025-01-18)
- 初始版本发布
- 完整的点赞/踩功能
- 国际化支持
- 响应式设计
- 安全性优化

## 技术支持

如有问题，请检查：
1. WordPress错误日志
2. 浏览器开发者工具控制台
3. 数据库表结构
4. 文件权限设置

## 许可证

本功能遵循WordPress主题的GPL许可证。
