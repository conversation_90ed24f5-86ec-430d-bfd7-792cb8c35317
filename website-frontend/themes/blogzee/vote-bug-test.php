<?php
/**
 * Vote Bug Test Page
 * Access: /wp-content/themes/blogzee/vote-bug-test.php
 * 
 * This page helps reproduce and debug the vote counting bug
 * 
 * @package Blogzee
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

// Handle actions
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'setup_test':
            // Clear all votes and set up test scenario
            global $wpdb;
            $table_name = $wpdb->prefix . 'blogzee_post_likes';
            
            // Get first post
            $post = get_posts(['numberposts' => 1, 'post_status' => 'publish']);
            if (!empty($post)) {
                $post_id = $post[0]->ID;
                
                // Clear existing votes
                $wpdb->delete($table_name, ['post_id' => $post_id]);
                
                // Add User A's vote
                $wpdb->insert($table_name, [
                    'user_id' => 1,
                    'post_id' => $post_id,
                    'vote_type' => 'like',
                    'vote_time' => current_time('mysql')
                ]);
                
                echo '<div style="background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;">
                    ✅ Test setup complete! User A (ID=1) has voted "Like" on post ' . $post_id . '
                </div>';
            }
            break;
            
        case 'clear_votes':
            global $wpdb;
            $table_name = $wpdb->prefix . 'blogzee_post_likes';
            $wpdb->query("DELETE FROM $table_name");
            echo '<div style="background: #d1ecf1; padding: 10px; margin: 10px 0; border-radius: 5px;">
                ℹ️ All votes cleared
            </div>';
            break;
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Vote Bug Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #fafafa; }
        .button { background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; border: none; cursor: pointer; }
        .button:hover { background: #005a87; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        
        /* Voting system styles */
        .blogzee-post-voting {
            margin: 2rem 0;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            position: relative;
        }
        .voting-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            align-items: center;
        }
        .vote-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            background: #ffffff;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            min-width: 100px;
            justify-content: center;
        }
        .like-btn { border-color: #28a745; color: #28a745; }
        .dislike-btn { border-color: #dc3545; color: #dc3545; }
        .vote-btn.active { background: #28a745; color: white; border-color: #28a745; }
        .dislike-btn.active { background: #dc3545; color: white; border-color: #dc3545; }
        .vote-count { font-weight: 600; min-width: 20px; text-align: center; }
        
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .vote-feedback, .vote-error {
            position: absolute;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
            padding: 0.75rem 1.25rem;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            white-space: nowrap;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        .vote-feedback {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .vote-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Vote Bug Test Page</h1>
        
        <div class="test-section">
            <h2>Test Setup</h2>
            <p>This page helps reproduce the vote counting bug where new users' votes incorrectly decrease existing counts.</p>
            
            <a href="?action=setup_test" class="button">Setup Test Scenario</a>
            <a href="?action=clear_votes" class="button danger">Clear All Votes</a>
            <a href="<?php echo home_url(); ?>" class="button">← Back to Site</a>
        </div>
        
        <?php
        // Get current vote status
        global $wpdb;
        $table_name = $wpdb->prefix . 'blogzee_post_likes';
        $post = get_posts(['numberposts' => 1, 'post_status' => 'publish']);
        
        if (!empty($post)) {
            $post_id = $post[0]->ID;
            $vote_counts = blogzee_get_post_vote_counts($post_id);
            $current_user_vote = blogzee_get_user_vote($post_id);
            
            // Get all votes for this post
            $all_votes = $wpdb->get_results($wpdb->prepare(
                "SELECT user_id, vote_type, vote_time FROM $table_name WHERE post_id = %d ORDER BY vote_time",
                $post_id
            ));
            
            echo '<div class="test-section">';
            echo '<h2>Current Status for Post: ' . get_the_title($post_id) . ' (ID: ' . $post_id . ')</h2>';
            echo '<p><strong>Current Counts:</strong> Likes: ' . $vote_counts['like_count'] . ', Dislikes: ' . $vote_counts['dislike_count'] . '</p>';
            echo '<p><strong>Your Vote:</strong> ' . ($current_user_vote ?: 'None') . '</p>';
            echo '<p><strong>Current User ID:</strong> ' . get_current_user_id() . '</p>';
            
            if ($all_votes) {
                echo '<h3>All Votes in Database:</h3>';
                echo '<table style="width: 100%; border-collapse: collapse;">';
                echo '<tr style="background: #f2f2f2;"><th style="border: 1px solid #ddd; padding: 8px;">User ID</th><th style="border: 1px solid #ddd; padding: 8px;">Vote Type</th><th style="border: 1px solid #ddd; padding: 8px;">Time</th></tr>';
                foreach ($all_votes as $vote) {
                    echo '<tr>';
                    echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $vote->user_id . '</td>';
                    echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $vote->vote_type . '</td>';
                    echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $vote->vote_time . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            } else {
                echo '<p><em>No votes in database</em></p>';
            }
            echo '</div>';
            
            // Show the voting interface
            echo '<div class="test-section">';
            echo '<h2>Test Voting Interface</h2>';
            echo '<p><strong>Instructions:</strong></p>';
            echo '<ol>';
            echo '<li>First, click "Setup Test Scenario" above to create User A\'s vote</li>';
            echo '<li>Then vote as the current user (User ' . get_current_user_id() . ') using the buttons below</li>';
            echo '<li>Watch the browser console and WordPress debug log for detailed information</li>';
            echo '<li>Expected: Counts should INCREASE when you vote (if you haven\'t voted before)</li>';
            echo '<li>Bug: Counts DECREASE instead of increasing</li>';
            echo '</ol>';
            
            echo '<div class="blogzee-post-voting" data-post-id="' . $post_id . '">';
            echo '<div class="voting-buttons">';
            echo '<button class="vote-btn like-btn' . ($current_user_vote === 'like' ? ' active' : '') . '" data-vote-type="like">';
            echo '<i class="fa-solid fa-thumbs-up"></i>';
            echo '<span class="vote-text">Like</span>';
            echo '<span class="vote-count">' . $vote_counts['like_count'] . '</span>';
            echo '</button>';
            echo '<button class="vote-btn dislike-btn' . ($current_user_vote === 'dislike' ? ' active' : '') . '" data-vote-type="dislike">';
            echo '<i class="fa-solid fa-thumbs-down"></i>';
            echo '<span class="vote-text">Dislike</span>';
            echo '<span class="vote-count">' . $vote_counts['dislike_count'] . '</span>';
            echo '</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
        ?>
        
        <div class="test-section">
            <h2>Debug Console</h2>
            <p>Watch this area for real-time debug information:</p>
            <div id="debugLog" class="debug-log">Debug information will appear here...</div>
            <button onclick="clearDebugLog()" class="button">Clear Log</button>
        </div>
    </div>

    <script>
        // WordPress AJAX variables
        const ajaxUrl = '<?php echo admin_url('admin-ajax.php'); ?>';
        const wpnonce = '<?php echo wp_create_nonce('blogzee-security-nonce'); ?>';
        
        // Debug logging function
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debugLog');
            logElement.innerHTML += '[' + timestamp + '] ' + message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            console.log('Vote Debug:', message);
        }
        
        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = 'Debug log cleared...\n';
        }
        
        // Initialize debug logging
        debugLog('Page loaded. AJAX URL: ' + ajaxUrl);
        debugLog('Nonce: ' + wpnonce);
        
        // Voting system implementation (same as theme.js but with extra debugging)
        $('.blogzee-post-voting').each(function() {
            const votingContainer = $(this);
            const postId = votingContainer.data('post-id');
            
            debugLog('Voting container initialized for post ' + postId);
            
            votingContainer.on('click', '.vote-btn', function(e) {
                e.preventDefault();
                
                const button = $(this);
                const voteType = button.data('vote-type');
                
                debugLog('Vote button clicked: ' + voteType + ' for post ' + postId);
                
                // Get current counts before voting
                const currentLikeCount = votingContainer.find('.like-btn .vote-count').text();
                const currentDislikeCount = votingContainer.find('.dislike-btn .vote-count').text();
                debugLog('Current counts BEFORE vote: Likes=' + currentLikeCount + ', Dislikes=' + currentDislikeCount);
                
                // Disable buttons during request
                votingContainer.find('.vote-btn').prop('disabled', true);
                button.addClass('loading');
                
                // Send AJAX request
                $.ajax({
                    url: ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'blogzee_post_vote',
                        post_id: postId,
                        vote_type: voteType,
                        nonce: wpnonce
                    },
                    success: function(response) {
                        debugLog('AJAX response received: ' + JSON.stringify(response));
                        
                        if (response.success) {
                            const data = response.data;
                            debugLog('Vote action: ' + data.action);
                            debugLog('New counts from server: Likes=' + data.like_count + ', Dislikes=' + data.dislike_count);
                            debugLog('User vote: ' + (data.user_vote || 'none'));
                            
                            // Update vote counts
                            votingContainer.find('.like-btn .vote-count').text(data.like_count);
                            votingContainer.find('.dislike-btn .vote-count').text(data.dislike_count);
                            
                            // Update button states
                            votingContainer.find('.vote-btn').removeClass('active');
                            if (data.user_vote) {
                                votingContainer.find('.vote-btn[data-vote-type="' + data.user_vote + '"]').addClass('active');
                            }
                            
                            // Show feedback
                            let message = '';
                            switch(data.action) {
                                case 'added': message = 'Vote added!'; break;
                                case 'updated': message = 'Vote updated!'; break;
                                case 'removed': message = 'Vote removed!'; break;
                            }
                            
                            if (message) {
                                const feedback = $('<div class="vote-feedback">' + message + '</div>');
                                votingContainer.css('position', 'relative');
                                votingContainer.find('.vote-feedback').remove();
                                votingContainer.append(feedback);
                                feedback.fadeIn(200).delay(2000).fadeOut(200, function() {
                                    feedback.remove();
                                });
                            }
                            
                            debugLog('Vote processing completed successfully');
                            
                            // Reload page after 3 seconds to see updated database state
                            setTimeout(function() {
                                debugLog('Reloading page to show updated state...');
                                window.location.reload();
                            }, 3000);
                            
                        } else {
                            debugLog('AJAX error: ' + (response.data ? response.data.message : 'Unknown error'));
                            const errorMsg = $('<div class="vote-error">' + (response.data ? response.data.message : 'An error occurred') + '</div>');
                            votingContainer.css('position', 'relative');
                            votingContainer.find('.vote-error').remove();
                            votingContainer.append(errorMsg);
                            errorMsg.fadeIn(200).delay(3000).fadeOut(200, function() {
                                errorMsg.remove();
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        debugLog('AJAX network error: ' + error);
                        const errorMsg = $('<div class="vote-error">Network error. Please try again.</div>');
                        votingContainer.css('position', 'relative');
                        votingContainer.find('.vote-error').remove();
                        votingContainer.append(errorMsg);
                        errorMsg.fadeIn(200).delay(3000).fadeOut(200, function() {
                            errorMsg.remove();
                        });
                    },
                    complete: function() {
                        votingContainer.find('.vote-btn').prop('disabled', false).removeClass('loading');
                    }
                });
            });
        });
    </script>
</body>
</html>
