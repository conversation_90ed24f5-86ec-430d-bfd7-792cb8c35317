<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Voting System Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Include the voting system CSS for testing */
        .blogzee-post-voting {
            margin: 2rem 0;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .voting-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            align-items: center;
        }

        .vote-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            background: #ffffff;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            min-width: 100px;
            justify-content: center;
        }

        .like-btn {
            border-color: #28a745;
            color: #28a745;
        }

        .dislike-btn {
            border-color: #dc3545;
            color: #dc3545;
        }

        .vote-count {
            font-weight: 600;
            min-width: 20px;
            text-align: center;
        }

        /* Post like rate styles */
        .post-like-rate {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-left: 1rem;
            font-size: 0.85rem;
        }

        .like-rate-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-weight: 500;
            font-size: 0.8rem;
        }

        .rate-excellent {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .rate-good {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .rate-text {
            color: #6c757d;
            font-size: 0.75rem;
        }

        /* Test container styles */
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 1rem;
            font-family: Arial, sans-serif;
        }

        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .screen-size-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007cba;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            z-index: 1000;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .voting-buttons {
                flex-direction: row;
                gap: 0.75rem;
            }
            
            .vote-btn {
                flex: 1;
                max-width: 150px;
                min-height: 44px;
            }
        }

        @media (max-width: 480px) {
            .voting-buttons {
                gap: 0.5rem;
            }
            
            .vote-btn {
                min-height: 44px;
                padding: 0.75rem 0.75rem;
            }
            
            .vote-text {
                display: none;
            }
        }

        @media (max-width: 375px) {
            .voting-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .vote-btn {
                width: 100%;
                max-width: none;
            }
        }

        @media (max-width: 320px) {
            .vote-btn {
                min-height: 40px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="screen-size-indicator" id="screenSize">
        Screen: <span id="screenWidth"></span>px
    </div>

    <div class="test-container">
        <h1>Mobile Voting System Test</h1>
        
        <div class="test-section">
            <h2>Voting Buttons Test</h2>
            <p>Test the voting buttons at different screen sizes:</p>
            
            <div class="blogzee-post-voting" data-post-id="1">
                <div class="voting-buttons">
                    <button class="vote-btn like-btn" data-vote-type="like">
                        <i class="fa-solid fa-thumbs-up"></i>
                        <span class="vote-text">Like</span>
                        <span class="vote-count">15</span>
                    </button>
                    <button class="vote-btn dislike-btn" data-vote-type="dislike">
                        <i class="fa-solid fa-thumbs-down"></i>
                        <span class="vote-text">Dislike</span>
                        <span class="vote-count">3</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Like Rate Display Test</h2>
            <p>Test the like rate indicators in post lists:</p>
            
            <div class="entry-meta">
                <span>Posted on January 18, 2025</span>
                <div class="post-like-rate">
                    <span class="like-rate-indicator rate-excellent">
                        <i class="fa-solid fa-thumbs-up"></i>
                        <span class="rate-percentage">83.3%</span>
                    </span>
                    <span class="rate-text">18 votes</span>
                </div>
            </div>
            
            <div class="post-meta">
                <span>By Author Name</span>
                <div class="post-like-rate">
                    <span class="like-rate-indicator rate-good">
                        <i class="fa-solid fa-thumbs-up"></i>
                        <span class="rate-percentage">67.5%</span>
                    </span>
                    <span class="rate-text">12 votes</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Touch Test Instructions</h2>
            <ul>
                <li><strong>320px:</strong> Buttons should stack vertically, text hidden</li>
                <li><strong>375px:</strong> Buttons should stack vertically with full width</li>
                <li><strong>414px:</strong> Buttons should be side by side, text hidden</li>
                <li><strong>768px+:</strong> Full layout with text visible</li>
            </ul>
            
            <p><strong>Touch Requirements:</strong></p>
            <ul>
                <li>Minimum button height: 44px (Apple guidelines)</li>
                <li>Adequate spacing between buttons</li>
                <li>No hover effects on touch devices</li>
                <li>Tap highlight disabled</li>
            </ul>
        </div>
    </div>

    <script>
        // Display current screen width
        function updateScreenSize() {
            const width = window.innerWidth;
            document.getElementById('screenWidth').textContent = width;
            
            // Add visual indicators for breakpoints
            const indicator = document.getElementById('screenSize');
            if (width <= 320) {
                indicator.style.backgroundColor = '#dc3545';
                indicator.innerHTML = 'Screen: ' + width + 'px (Extra Small)';
            } else if (width <= 375) {
                indicator.style.backgroundColor = '#fd7e14';
                indicator.innerHTML = 'Screen: ' + width + 'px (Small Mobile)';
            } else if (width <= 414) {
                indicator.style.backgroundColor = '#ffc107';
                indicator.innerHTML = 'Screen: ' + width + 'px (Mobile)';
            } else if (width <= 768) {
                indicator.style.backgroundColor = '#20c997';
                indicator.innerHTML = 'Screen: ' + width + 'px (Tablet)';
            } else {
                indicator.style.backgroundColor = '#007cba';
                indicator.innerHTML = 'Screen: ' + width + 'px (Desktop)';
            }
        }

        // Update on load and resize
        updateScreenSize();
        window.addEventListener('resize', updateScreenSize);

        // Add touch device detection
        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
            document.body.classList.add('touch-device');
            console.log('Touch device detected');
        }

        // Test button interactions
        document.querySelectorAll('.vote-btn').forEach(button => {
            button.addEventListener('click', function() {
                console.log('Button clicked:', this.dataset.voteType);
                
                // Simulate vote count update
                const countElement = this.querySelector('.vote-count');
                const currentCount = parseInt(countElement.textContent);
                countElement.textContent = currentCount + 1;
                
                // Add visual feedback
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
