<?php
/**
 * Test file for the voting system
 * This file can be used to test the voting functionality
 * 
 * @package Blogzee
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test the voting system database table creation
 */
function test_voting_table_exists() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'blogzee_post_likes';
    
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if ($table_exists) {
        echo "✅ Database table '$table_name' exists.\n";
        
        // Check table structure
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        $expected_columns = ['id', 'user_id', 'post_id', 'vote_type', 'vote_time'];
        $actual_columns = array_column($columns, 'Field');
        
        $missing_columns = array_diff($expected_columns, $actual_columns);
        if (empty($missing_columns)) {
            echo "✅ All required columns exist in the table.\n";
        } else {
            echo "❌ Missing columns: " . implode(', ', $missing_columns) . "\n";
        }
    } else {
        echo "❌ Database table '$table_name' does not exist.\n";
    }
}

/**
 * Test the voting helper functions
 */
function test_voting_functions() {
    // Test with a sample post ID
    $test_post_id = 1;
    
    if (function_exists('blogzee_get_post_vote_counts')) {
        $vote_counts = blogzee_get_post_vote_counts($test_post_id);
        if (is_array($vote_counts) && isset($vote_counts['like_count']) && isset($vote_counts['dislike_count'])) {
            echo "✅ blogzee_get_post_vote_counts() function works correctly.\n";
            echo "   Like count: " . $vote_counts['like_count'] . ", Dislike count: " . $vote_counts['dislike_count'] . "\n";
        } else {
            echo "❌ blogzee_get_post_vote_counts() function returns invalid data.\n";
        }
    } else {
        echo "❌ blogzee_get_post_vote_counts() function does not exist.\n";
    }
    
    if (function_exists('blogzee_get_user_vote')) {
        $user_vote = blogzee_get_user_vote($test_post_id);
        echo "✅ blogzee_get_user_vote() function exists.\n";
        echo "   Current user vote: " . ($user_vote ? $user_vote : 'none') . "\n";
    } else {
        echo "❌ blogzee_get_user_vote() function does not exist.\n";
    }
}

/**
 * Test AJAX handler registration
 */
function test_ajax_handlers() {
    global $wp_filter;
    
    $ajax_actions = [
        'wp_ajax_blogzee_post_vote',
        'wp_ajax_nopriv_blogzee_post_vote'
    ];
    
    foreach ($ajax_actions as $action) {
        if (isset($wp_filter[$action])) {
            echo "✅ AJAX handler '$action' is registered.\n";
        } else {
            echo "❌ AJAX handler '$action' is not registered.\n";
        }
    }
}

/**
 * Test template modifications
 */
function test_template_modifications() {
    $content_single_path = get_template_directory() . '/template-parts/content-single.php';
    
    if (file_exists($content_single_path)) {
        $content = file_get_contents($content_single_path);
        
        if (strpos($content, 'blogzee-post-voting') !== false) {
            echo "✅ Voting HTML structure added to content-single.php.\n";
        } else {
            echo "❌ Voting HTML structure not found in content-single.php.\n";
        }
        
        if (strpos($content, 'blogzee_get_post_vote_counts') !== false) {
            echo "✅ Vote count function call found in template.\n";
        } else {
            echo "❌ Vote count function call not found in template.\n";
        }
    } else {
        echo "❌ content-single.php template file not found.\n";
    }
}

/**
 * Test CSS and JavaScript files
 */
function test_assets() {
    $css_path = get_template_directory() . '/style.css';
    $js_path = get_template_directory() . '/assets/js/theme.js';
    
    // Test CSS
    if (file_exists($css_path)) {
        $css_content = file_get_contents($css_path);
        if (strpos($css_content, 'blogzee-post-voting') !== false) {
            echo "✅ Voting CSS styles added to style.css.\n";
        } else {
            echo "❌ Voting CSS styles not found in style.css.\n";
        }
    } else {
        echo "❌ style.css file not found.\n";
    }
    
    // Test JavaScript
    if (file_exists($js_path)) {
        $js_content = file_get_contents($js_path);
        if (strpos($js_content, 'blogzee_post_vote') !== false) {
            echo "✅ Voting JavaScript functionality added to theme.js.\n";
        } else {
            echo "❌ Voting JavaScript functionality not found in theme.js.\n";
        }
    } else {
        echo "❌ theme.js file not found.\n";
    }
}

/**
 * Test internationalization
 */
function test_internationalization() {
    $pot_path = get_template_directory() . '/languages/blogzee.pot';
    
    if (file_exists($pot_path)) {
        $pot_content = file_get_contents($pot_path);
        $required_strings = ['Like', 'Dislike', 'You must be logged in to vote'];
        $missing_strings = [];
        
        foreach ($required_strings as $string) {
            if (strpos($pot_content, $string) === false) {
                $missing_strings[] = $string;
            }
        }
        
        if (empty($missing_strings)) {
            echo "✅ All required translation strings found in blogzee.pot.\n";
        } else {
            echo "❌ Missing translation strings: " . implode(', ', $missing_strings) . "\n";
        }
    } else {
        echo "❌ blogzee.pot file not found.\n";
    }
}

/**
 * Run all tests
 */
function run_voting_system_tests() {
    echo "<h2>Blogzee Voting System Test Results</h2>\n";
    echo "<pre>\n";
    
    echo "=== Database Tests ===\n";
    test_voting_table_exists();
    echo "\n";
    
    echo "=== Function Tests ===\n";
    test_voting_functions();
    echo "\n";
    
    echo "=== AJAX Handler Tests ===\n";
    test_ajax_handlers();
    echo "\n";
    
    echo "=== Template Tests ===\n";
    test_template_modifications();
    echo "\n";
    
    echo "=== Asset Tests ===\n";
    test_assets();
    echo "\n";
    
    echo "=== Internationalization Tests ===\n";
    test_internationalization();
    echo "\n";
    
    echo "=== Test Complete ===\n";
    echo "</pre>\n";
}

// Run tests if this file is accessed directly (for debugging purposes)
if (isset($_GET['run_tests']) && current_user_can('manage_options')) {
    run_voting_system_tests();
}
